{"rustc": 16591470773350601817, "features": "[\"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\", \"test-utils\"]", "target": 12987313468403878510, "profile": 13136499164997004105, "path": 9125880921305846468, "deps": [[530211389790465181, "hex", false, 17088062159765854285], [5994642257694095480, "forge_ec_core", false, 11215900732348816337], [6528079939221783635, "zeroize", false, 2745998783931530775], [9209347893430674936, "hmac", false, 15271584293396208319], [9857275760291862238, "sha2", false, 15044962170313179095], [13326029724636836038, "forge_ec_rng", false, 17231731706523192846], [14350836257056350918, "forge_ec_curves", false, 10294871808448728720], [15063701716282871955, "forge_ec_hash", false, 5416292730735005866], [17003143334332120809, "subtle", false, 2669115221904121209], [17475753849556516473, "digest", false, 10835082637553113463], [18130209639506977569, "rand_core", false, 2614454316829272266]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\forge-ec-signature-f6453c598ca1a39a\\dep-lib-forge_ec_signature", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}