/* ===== ADVANCED ANIMATIONS & EFFECTS ===== */

/* Hardware Acceleration */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== ENTRANCE ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* ===== SCROLL TRIGGERED ANIMATIONS ===== */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translateY(0);
}

.animate-on-scroll.delay-1 { transition-delay: 0.1s; }
.animate-on-scroll.delay-2 { transition-delay: 0.2s; }
.animate-on-scroll.delay-3 { transition-delay: 0.3s; }
.animate-on-scroll.delay-4 { transition-delay: 0.4s; }
.animate-on-scroll.delay-5 { transition-delay: 0.5s; }

/* ===== MAGNETIC HOVER EFFECTS ===== */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic:hover {
  transform: scale(1.05);
}

.magnetic-strong:hover {
  transform: scale(1.1) translateY(-5px);
}

/* ===== GLASS MORPHISM EFFECTS ===== */
.glass {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass);
  box-shadow: var(--shadow-glass);
}

.glass-hover {
  transition: all var(--transition-normal);
}

.glass-hover:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
}

/* ===== PARTICLE EFFECTS ===== */
.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--color-primary);
  border-radius: 50%;
  opacity: 0.6;
  animation: particleFloat 20s linear infinite;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100px) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

/* ===== TYPEWRITER EFFECT ===== */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--color-primary);
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--color-primary); }
}

/* ===== RIPPLE EFFECT ===== */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}

/* ===== FLOATING ELEMENTS ===== */
.float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.float-delayed {
  animation: float 6s ease-in-out infinite;
  animation-delay: -2s;
}

/* ===== PULSE EFFECTS ===== */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from {
    box-shadow: 0 0 20px var(--color-primary);
  }
  to {
    box-shadow: 0 0 40px var(--color-primary), 0 0 60px var(--color-primary);
  }
}

/* ===== ROTATION EFFECTS ===== */
.rotate-slow {
  animation: rotateSlow 20s linear infinite;
}

@keyframes rotateSlow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotate-hover {
  transition: transform var(--transition-normal);
}

.rotate-hover:hover {
  transform: rotate(5deg);
}

/* ===== SHIMMER EFFECTS ===== */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* ===== MORPHING EFFECTS ===== */
.morph {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.morph-circle {
  border-radius: 50%;
}

.morph-square {
  border-radius: 0;
}

.morph-rounded {
  border-radius: var(--radius-2xl);
}

/* ===== PARALLAX EFFECTS ===== */
.parallax {
  transform: translateZ(0);
  will-change: transform;
}

.parallax-slow {
  transform: translateY(var(--parallax-offset-slow, 0));
}

.parallax-medium {
  transform: translateY(var(--parallax-offset-medium, 0));
}

.parallax-fast {
  transform: translateY(var(--parallax-offset-fast, 0));
}

/* ===== STAGGER ANIMATIONS ===== */
.stagger-item {
  opacity: 0;
  transform: translateY(30px);
  animation: staggerFadeIn 0.6s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes staggerFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== MICRO INTERACTIONS ===== */
.micro-bounce {
  transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.micro-bounce:hover {
  transform: scale(1.05);
}

.micro-bounce:active {
  transform: scale(0.95);
}

.micro-slide {
  transition: transform var(--transition-fast);
}

.micro-slide:hover {
  transform: translateX(5px);
}

/* ===== LOADING ANIMATIONS ===== */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dots-loading {
  display: inline-block;
}

.dots-loading::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* ===== REVEAL ANIMATIONS ===== */
.reveal-mask {
  overflow: hidden;
}

.reveal-content {
  transform: translateY(100%);
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.reveal-mask.revealed .reveal-content {
  transform: translateY(0);
}

/* ===== ELASTIC ANIMATIONS ===== */
.elastic {
  animation: elastic 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes elastic {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* Remove will-change after animation */
.animation-complete {
  will-change: auto;
}

/* ===== BLOB RIPPLE ANIMATION ===== */
@keyframes blobRipple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

.blob-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  transform: translate(-50%, -50%);
  animation: blobRipple 600ms ease-out;
  pointer-events: none;
  z-index: 1;
}

/* ===== RIPPLE ANIMATION ===== */
@keyframes ripple-animation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.ripple-effect {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

/* ===== MORPH BUTTON EFFECTS ===== */
.morph-button {
  transition: clip-path 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);
}

.morph-button:hover {
  clip-path: polygon(20% 0%, 80% 0%, 100% 20%, 100% 80%, 80% 100%, 20% 100%, 0% 80%, 0% 20%);
}

/* ===== ENHANCED MAGNETIC EFFECTS ===== */
.magnetic-field {
  position: relative;
  transition: all 150ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic-field::before {
  content: '';
  position: absolute;
  top: -50px;
  left: -50px;
  right: -50px;
  bottom: -50px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 200ms ease;
  pointer-events: none;
  z-index: -1;
}

.magnetic-field:hover::before {
  opacity: 1;
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .parallax {
    transform: none !important;
  }

  .particle {
    display: none;
  }

  .magnetic,
  .magnetic-field,
  .morph-button {
    transform: none !important;
    clip-path: none !important;
  }
}
