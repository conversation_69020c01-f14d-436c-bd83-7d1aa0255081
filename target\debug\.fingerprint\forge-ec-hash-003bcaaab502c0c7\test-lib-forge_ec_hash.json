{"rustc": 16591470773350601817, "features": "[\"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"forge-ec-curves\", \"hex-literal\", \"rand_core\", \"std\", \"test-utils\"]", "target": 10889075618143456102, "profile": 1235621381506040755, "path": 6748637631826849034, "deps": [[5994642257694095480, "forge_ec_core", false, 12275727600174720818], [6528079939221783635, "zeroize", false, 3900412342606702231], [8700459469608572718, "blake2", false, 8520962200500480487], [9857275760291862238, "sha2", false, 10584952717619411474], [11017232866922121725, "sha3", false, 4757858998616045413], [17003143334332120809, "subtle", false, 4917266028617495969], [17475753849556516473, "digest", false, 16341302406531691146]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\forge-ec-hash-003bcaaab502c0c7\\dep-test-lib-forge_ec_hash", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}