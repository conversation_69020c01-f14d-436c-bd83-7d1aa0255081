{"$message_type":"diagnostic","message":"unused import: `KeyExchange`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":60154,"byte_end":60165,"line_start":2044,"line_end":2044,"column_start":25,"column_end":36,"is_primary":true,"text":[{"text":"    use forge_ec_core::{KeyExchange, HashToCurve};","highlight_start":25,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":60154,"byte_end":60167,"line_start":2044,"line_end":2044,"column_start":25,"column_end":38,"is_primary":true,"text":[{"text":"    use forge_ec_core::{KeyExchange, HashToCurve};","highlight_start":25,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":60153,"byte_end":60154,"line_start":2044,"line_end":2044,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"    use forge_ec_core::{KeyExchange, HashToCurve};","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":60178,"byte_end":60179,"line_start":2044,"line_end":2044,"column_start":49,"column_end":50,"is_primary":true,"text":[{"text":"    use forge_ec_core::{KeyExchange, HashToCurve};","highlight_start":49,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `KeyExchange`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-curves\\src\\p256.rs:2044:25\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2044\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use forge_ec_core::{KeyExchange, HashToCurve};\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `test_point`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-curves\\src\\secp256k1.rs","byte_start":96976,"byte_end":96986,"line_start":2987,"line_end":2987,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let test_point = AffinePoint {","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-curves\\src\\secp256k1.rs","byte_start":96976,"byte_end":96986,"line_start":2987,"line_end":2987,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let test_point = AffinePoint {","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_test_point","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `test_point`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-curves\\src\\secp256k1.rs:2987:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2987\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let test_point = AffinePoint {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_test_point`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `g2`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":61374,"byte_end":61376,"line_start":2086,"line_end":2086,"column_start":13,"column_end":15,"is_primary":true,"text":[{"text":"        let g2 = P256::multiply(&g, &two);","highlight_start":13,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":61374,"byte_end":61376,"line_start":2086,"line_end":2086,"column_start":13,"column_end":15,"is_primary":true,"text":[{"text":"        let g2 = P256::multiply(&g, &two);","highlight_start":13,"highlight_end":15}],"label":null,"suggested_replacement":"_g2","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `g2`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-curves\\src\\p256.rs:2086:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2086\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let g2 = P256::multiply(&g, &two);\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_g2`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `g_doubled`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":61418,"byte_end":61427,"line_start":2087,"line_end":2087,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"        let g_doubled = g.double();","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":61418,"byte_end":61427,"line_start":2087,"line_end":2087,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"        let g_doubled = g.double();","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":"_g_doubled","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `g_doubled`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-curves\\src\\p256.rs:2087:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2087\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let g_doubled = g.double();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_g_doubled`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `alice_pk`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":62026,"byte_end":62034,"line_start":2104,"line_end":2104,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let alice_pk = P256::to_affine(&P256::multiply(&P256::generator(), &alice_sk));","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":62026,"byte_end":62034,"line_start":2104,"line_end":2104,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let alice_pk = P256::to_affine(&P256::multiply(&P256::generator(), &alice_sk));","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":"_alice_pk","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `alice_pk`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-curves\\src\\p256.rs:2104:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let alice_pk = P256::to_affine(&P256::multiply(&P256::generator(), &alice_sk));\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_alice_pk`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `bob_pk`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":62162,"byte_end":62168,"line_start":2107,"line_end":2107,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"        let bob_pk = P256::to_affine(&P256::multiply(&P256::generator(), &bob_sk));","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":62162,"byte_end":62168,"line_start":2107,"line_end":2107,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"        let bob_pk = P256::to_affine(&P256::multiply(&P256::generator(), &bob_sk));","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":"_bob_pk","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `bob_pk`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-curves\\src\\p256.rs:2107:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2107\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let bob_pk = P256::to_affine(&P256::multiply(&P256::generator(), &bob_sk));\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_bob_pk`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `point`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":62534,"byte_end":62539,"line_start":2118,"line_end":2118,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        let point = P256::map_to_curve(&field_elem);","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-curves\\src\\p256.rs","byte_start":62534,"byte_end":62539,"line_start":2118,"line_end":2118,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        let point = P256::map_to_curve(&field_elem);","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":"_point","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `point`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-curves\\src\\p256.rs:2118:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let point = P256::map_to_curve(&field_elem);\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_point`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `point_1`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-curves\\src\\ed25519.rs","byte_start":76677,"byte_end":76684,"line_start":2412,"line_end":2412,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"        let point_1 = Ed25519::multiply(&g, &scalar_1);","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-curves\\src\\ed25519.rs","byte_start":76677,"byte_end":76684,"line_start":2412,"line_end":2412,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"        let point_1 = Ed25519::multiply(&g, &scalar_1);","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":"_point_1","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `point_1`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-curves\\src\\ed25519.rs:2412:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2412\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let point_1 = Ed25519::multiply(&g, &scalar_1);\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_point_1`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `random_point`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-curves\\src\\ed25519.rs","byte_start":77606,"byte_end":77618,"line_start":2436,"line_end":2436,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let random_point = Ed25519::multiply(&g, &random_scalar);","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-curves\\src\\ed25519.rs","byte_start":77606,"byte_end":77618,"line_start":2436,"line_end":2436,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let random_point = Ed25519::multiply(&g, &random_scalar);","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_random_point","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `random_point`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-curves\\src\\ed25519.rs:2436:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2436\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let random_point = Ed25519::multiply(&g, &random_scalar);\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_random_point`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error finalizing incremental compilation session directory `\\\\?\\C:\\Users\\<USER>\\OneDrive\\Desktop\\forge-ec\\target\\debug\\incremental\\forge_ec_curves-3rbdwurb2vt0r\\s-h7p9lpj1k6-08uegoa-working`: Access is denied. (os error 5)","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error finalizing incremental compilation session directory `\\\\?\\C:\\Users\\<USER>\\OneDrive\\Desktop\\forge-ec\\target\\debug\\incremental\\forge_ec_curves-3rbdwurb2vt0r\\s-h7p9lpj1k6-08uegoa-working`: Access is denied. (os error 5)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"10 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 10 warnings emitted\u001b[0m\n\n"}
