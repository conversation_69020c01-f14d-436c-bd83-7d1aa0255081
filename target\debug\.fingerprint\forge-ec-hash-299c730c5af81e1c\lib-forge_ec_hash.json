{"rustc": 16591470773350601817, "features": "[\"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"forge-ec-curves\", \"hex-literal\", \"rand_core\", \"std\", \"test-utils\"]", "target": 10889075618143456102, "profile": 13136499164997004105, "path": 6748637631826849034, "deps": [[5994642257694095480, "forge_ec_core", false, 11215900732348816337], [6528079939221783635, "zeroize", false, 2745998783931530775], [8700459469608572718, "blake2", false, 17549589154143598188], [9857275760291862238, "sha2", false, 15044962170313179095], [11017232866922121725, "sha3", false, 1263397073067862669], [17003143334332120809, "subtle", false, 2669115221904121209], [17475753849556516473, "digest", false, 10835082637553113463]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\forge-ec-hash-299c730c5af81e1c\\dep-lib-forge_ec_hash", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}