{"rustc": 16591470773350601817, "features": "[\"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\", \"test-utils\"]", "target": 12987313468403878510, "profile": 15396524706607348604, "path": 9125880921305846468, "deps": [[530211389790465181, "hex", false, 7681383813274821952], [5994642257694095480, "forge_ec_core", false, 12275727600174720818], [6528079939221783635, "zeroize", false, 3900412342606702231], [9209347893430674936, "hmac", false, 154507136758044380], [9857275760291862238, "sha2", false, 10584952717619411474], [13326029724636836038, "forge_ec_rng", false, 15468077903753589964], [14350836257056350918, "forge_ec_curves", false, 14418558587460361318], [15063701716282871955, "forge_ec_hash", false, 15901251101925837570], [17003143334332120809, "subtle", false, 4917266028617495969], [17475753849556516473, "digest", false, 16341302406531691146], [18130209639506977569, "rand_core", false, 2072236099581160293]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\forge-ec-signature-d71ac74f5489af40\\dep-lib-forge_ec_signature", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}