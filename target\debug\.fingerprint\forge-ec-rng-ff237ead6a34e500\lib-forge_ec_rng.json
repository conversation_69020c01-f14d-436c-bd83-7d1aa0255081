{"rustc": 16591470773350601817, "features": "[\"default\", \"hex\", \"hex-literal\", \"std\", \"test-utils\"]", "declared_features": "[\"alloc\", \"default\", \"hex\", \"hex-literal\", \"std\", \"test-utils\"]", "target": 14952468957906770697, "profile": 15396524706607348604, "path": 14309710185945734409, "deps": [[530211389790465181, "hex", false, 7681383813274821952], [5994642257694095480, "forge_ec_core", false, 12275727600174720818], [6528079939221783635, "zeroize", false, 3900412342606702231], [8632578124021956924, "hex_literal", false, 4473380097892883435], [9209347893430674936, "hmac", false, 154507136758044380], [9857275760291862238, "sha2", false, 10584952717619411474], [9920160576179037441, "getrandom", false, 5792374328201171034], [14350836257056350918, "forge_ec_curves", false, 14418558587460361318], [15063701716282871955, "forge_ec_hash", false, 15901251101925837570], [17003143334332120809, "subtle", false, 4917266028617495969], [18130209639506977569, "rand_core", false, 2072236099581160293]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\forge-ec-rng-ff237ead6a34e500\\dep-lib-forge_ec_rng", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}