{"$message_type":"diagnostic","message":"unused variable: `public_keys`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":28080,"byte_end":28091,"line_start":787,"line_end":787,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"        let public_keys = vec![pk_affine];","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":28080,"byte_end":28091,"line_start":787,"line_end":787,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"        let public_keys = vec![pk_affine];","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":"_public_keys","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `public_keys`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-signature\\src\\schnorr.rs:787:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m787\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let public_keys = vec![pk_affine];\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_public_keys`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `messages`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":28124,"byte_end":28132,"line_start":788,"line_end":788,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let messages = vec![msg as &[u8]];","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":28124,"byte_end":28132,"line_start":788,"line_end":788,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let messages = vec![msg as &[u8]];","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":"_messages","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `messages`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-signature\\src\\schnorr.rs:788:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m788\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let messages = vec![msg as &[u8]];\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_messages`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `signatures`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":28168,"byte_end":28178,"line_start":789,"line_end":789,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let signatures = vec![sig];","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":28168,"byte_end":28178,"line_start":789,"line_end":789,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let signatures = vec![sig];","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_signatures","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `signatures`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-signature\\src\\schnorr.rs:789:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m789\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let signatures = vec![sig];\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_signatures`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `modified_messages`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":28576,"byte_end":28593,"line_start":799,"line_end":799,"column_start":13,"column_end":30,"is_primary":true,"text":[{"text":"        let modified_messages = vec![modified_msg as &[u8]];","highlight_start":13,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":28576,"byte_end":28593,"line_start":799,"line_end":799,"column_start":13,"column_end":30,"is_primary":true,"text":[{"text":"        let modified_messages = vec![modified_msg as &[u8]];","highlight_start":13,"highlight_end":30}],"label":null,"suggested_replacement":"_modified_messages","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `modified_messages`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-signature\\src\\schnorr.rs:799:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m799\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let modified_messages = vec![modified_msg as &[u8]];\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_modified_messages`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `public_keys`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":29785,"byte_end":29796,"line_start":826,"line_end":826,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"        let public_keys = vec![&expected_public_key_array];","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":29785,"byte_end":29796,"line_start":826,"line_end":826,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"        let public_keys = vec![&expected_public_key_array];","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":"_public_keys","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `public_keys`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-signature\\src\\schnorr.rs:826:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m826\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let public_keys = vec![&expected_public_key_array];\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_public_keys`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `messages`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":29846,"byte_end":29854,"line_start":827,"line_end":827,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let messages: Vec<&[u8]> = vec![msg as &[u8]];","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":29846,"byte_end":29854,"line_start":827,"line_end":827,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let messages: Vec<&[u8]> = vec![msg as &[u8]];","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":"_messages","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `messages`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-signature\\src\\schnorr.rs:827:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m827\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let messages: Vec<&[u8]> = vec![msg as &[u8]];\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_messages`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `signatures`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":29902,"byte_end":29912,"line_start":828,"line_end":828,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let signatures = vec![&signature];","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"forge-ec-signature\\src\\schnorr.rs","byte_start":29902,"byte_end":29912,"line_start":828,"line_end":828,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let signatures = vec![&signature];","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_signatures","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `signatures`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mforge-ec-signature\\src\\schnorr.rs:828:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m828\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let signatures = vec![&signature];\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_signatures`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"7 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 7 warnings emitted\u001b[0m\n\n"}
