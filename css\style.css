/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Colors - Light Theme */
  --color-primary: #3b82f6;
  --color-secondary: #8b5cf6;
  --color-accent: #06b6d4;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  /* Backgrounds */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-glass: rgba(255, 255, 255, 0.1);
  --bg-glass-hover: rgba(255, 255, 255, 0.2);

  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-inverse: #ffffff;

  /* Borders */
  --border-color: rgba(148, 163, 184, 0.2);
  --border-glass: rgba(255, 255, 255, 0.2);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  --gradient-accent: linear-gradient(135deg, var(--color-accent) 0%, var(--color-primary) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

  /* Typography */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-glass: rgba(0, 0, 0, 0.2);
  --bg-glass-hover: rgba(0, 0, 0, 0.3);

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;

  --border-color: rgba(148, 163, 184, 0.1);
  --border-glass: rgba(255, 255, 255, 0.1);

  --shadow-glass: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
  --gradient-glass: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.1) 100%);
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
  overflow-x: hidden;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
  color: white;
}

.forge-logo-container {
  margin-bottom: var(--space-6);
}

.forge-logo {
  width: 80px;
  height: 80px;
  animation: logoSpin 2s ease-in-out infinite;
}

.logo-path {
  stroke-dasharray: 200;
  stroke-dashoffset: 200;
  animation: logoPathDraw 2s ease-in-out infinite;
}

.logo-dot {
  opacity: 0;
  animation: logoDotFade 2s ease-in-out infinite;
}

.loading-text {
  font-size: var(--text-3xl);
  font-weight: 600;
  margin-bottom: var(--space-6);
  letter-spacing: 0.1em;
}

.loading-letter {
  display: inline-block;
  opacity: 0;
  animation: letterFadeIn 0.5s ease-out forwards;
}

.loading-letter:nth-child(1) { animation-delay: 0.1s; }
.loading-letter:nth-child(2) { animation-delay: 0.2s; }
.loading-letter:nth-child(3) { animation-delay: 0.3s; }
.loading-letter:nth-child(4) { animation-delay: 0.4s; }
.loading-letter:nth-child(5) { animation-delay: 0.5s; }
.loading-letter:nth-child(7) { animation-delay: 0.7s; }
.loading-letter:nth-child(8) { animation-delay: 0.8s; }

.loading-space {
  width: 0.5em;
  display: inline-block;
}

.loading-progress {
  width: 200px;
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin: 0 auto;
}

.progress-bar {
  width: 0%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 1) 100%);
  border-radius: var(--radius-full);
  animation: progressFill 3s ease-out forwards;
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-glass);
  transition: all var(--transition-normal);
}

.navbar.scrolled {
  background: var(--bg-glass-hover);
  box-shadow: var(--shadow-glass);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-weight: 600;
  font-size: var(--text-lg);
  color: var(--text-primary);
  text-decoration: none;
}

.brand-logo {
  width: 32px;
  height: 32px;
  color: var(--color-primary);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.nav-link {
  position: relative;
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--color-primary);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
  transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.theme-toggle {
  position: relative;
  width: 40px;
  height: 40px;
  border: none;
  background: var(--bg-glass);
  border-radius: var(--radius-lg);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-toggle:hover {
  background: var(--bg-glass-hover);
  transform: scale(1.05);
}

.theme-icon {
  width: 20px;
  height: 20px;
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.moon-icon {
  position: absolute;
  opacity: 0;
  transform: rotate(180deg);
}

[data-theme="dark"] .sun-icon {
  opacity: 0;
  transform: rotate(180deg);
}

[data-theme="dark"] .moon-icon {
  opacity: 1;
  transform: rotate(0deg);
}

.github-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.github-btn:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.github-btn svg {
  width: 20px;
  height: 20px;
}

.github-stats {
  display: flex;
  gap: var(--space-3);
  font-size: var(--text-sm);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: var(--space-2);
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background: var(--text-primary);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

/* ===== MAIN CONTENT ===== */
.main-content {
  padding-top: 70px;
}

/* ===== HERO SECTION ===== */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.hero-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  padding: 0 var(--space-6);
}

.hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: var(--space-6);
}

.title-line {
  display: block;
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--text-xl);
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.highlight {
  color: var(--color-primary);
  font-weight: 600;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-10);
  flex-wrap: wrap;
}

.feature-badge {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 500;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
}

.feature-badge:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.badge-icon {
  font-size: var(--text-lg);
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
  flex-wrap: wrap;
}

.cta-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  border: none;
  border-radius: var(--radius-xl);
  font-size: var(--text-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.cta-button.primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-lg);
}

.cta-button.primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.cta-button.secondary {
  background: var(--bg-glass);
  color: var(--text-primary);
  border: 1px solid var(--border-glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.cta-button.secondary:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-2px);
}

.btn-icon {
  width: 20px;
  height: 20px;
  transition: transform var(--transition-fast);
}

.cta-button:hover .btn-icon {
  transform: translateX(4px);
}

.installation-preview {
  max-width: 400px;
  margin: 0 auto;
}

.code-block {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-xl);
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  max-width: 100%;
  min-width: 0;
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-glass-hover);
  border-bottom: 1px solid var(--border-glass);
}

.code-title {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.copy-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.copy-btn:hover {
  color: var(--color-primary);
  background: var(--bg-glass);
}

.copy-btn svg {
  width: 16px;
  height: 16px;
}

.code-content {
  padding: var(--space-4);
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: var(--text-primary);
  background: transparent;
  border: none;
  outline: none;
  white-space: pre;
  overflow-x: auto;
  overflow-y: hidden;
  max-width: 100%;
  min-width: 0;
  /* Custom scrollbar for webkit browsers */
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary) var(--bg-tertiary);
}

/* Custom scrollbar styles for code-content */
.code-content::-webkit-scrollbar {
  height: 8px;
}

.code-content::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

.code-content::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 4px;
  opacity: 0.7;
}

.code-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-secondary);
  opacity: 1;
}

.scroll-indicator {
  position: absolute;
  bottom: var(--space-8);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-secondary);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 24px;
  height: 24px;
}

.scroll-text {
  font-size: var(--text-sm);
  font-weight: 500;
}

/* ===== GITHUB STATS SECTION ===== */
.github-stats-section {
  padding: var(--space-20) 0;
  background: var(--bg-secondary);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.stat-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.stat-icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-4);
}

.stat-value {
  font-size: var(--text-4xl);
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--space-2);
}

.stat-label {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  font-weight: 500;
}

/* ===== ANIMATIONS ===== */
@keyframes logoSpin {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

@keyframes logoPathDraw {
  0% { stroke-dashoffset: 200; }
  50% { stroke-dashoffset: 0; }
  100% { stroke-dashoffset: -200; }
}

@keyframes logoDotFade {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

@keyframes letterFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
  from {
    opacity: 0;
    transform: translateY(20px);
  }
}

@keyframes progressFill {
  to { width: 100%; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40%, 43% {
    transform: translateX(-50%) translateY(-10px);
  }
  70% {
    transform: translateX(-50%) translateY(-5px);
  }
  90% {
    transform: translateX(-50%) translateY(-2px);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .github-stats {
    display: none;
  }

  .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== ABOUT SECTION ===== */
.about-section {
  padding: var(--space-20) 0;
  background: var(--bg-secondary);
}

.subsection-title {
  font-size: var(--text-3xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  text-align: center;
}

/* Mission Statement */
.mission-container {
  margin-bottom: var(--space-16);
}

.mission-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-10);
  text-align: center;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
}

.mission-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.mission-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--space-6);
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.mission-icon svg {
  width: 40px;
  height: 40px;
}

.mission-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.mission-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: var(--space-8);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.mission-principles {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  max-width: 600px;
  margin: 0 auto;
}

.principle {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
}

.principle:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.principle-icon {
  font-size: var(--text-lg);
}

.principle-text {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
}

/* Team Section */
.team-section {
  margin-bottom: var(--space-16);
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-8);
}

.team-member {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
}

.team-member:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.member-avatar {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto var(--space-6);
}

.member-avatar img {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-full);
  object-fit: cover;
  border: 3px solid var(--color-primary);
}

.avatar-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid transparent;
  border-radius: var(--radius-full);
  background: var(--gradient-primary);
  background-clip: padding-box;
  animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

.member-info {
  text-align: center;
}

.member-name {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.member-role {
  font-size: var(--text-lg);
  color: var(--color-primary);
  font-weight: 500;
  margin-bottom: var(--space-4);
}

.member-bio {
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.member-links {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
}

.member-link {
  width: 40px;
  height: 40px;
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.member-link:hover {
  color: var(--color-primary);
  background: var(--bg-glass-hover);
  transform: translateY(-2px);
}

.member-link svg {
  width: 20px;
  height: 20px;
}

/* Contributors Card */
.contributors-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
}

.contributors-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.contributors-header {
  text-align: center;
  margin-bottom: var(--space-6);
}

.contributors-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.contributors-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
}

.contributors-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: var(--space-3);
  margin-bottom: var(--space-6);
}

.contributor-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.contributor-avatar {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full);
  border: 2px solid var(--border-glass);
  transition: all var(--transition-fast);
}

.contributor-avatar:hover {
  transform: scale(1.1);
  border-color: var(--color-primary);
}

.contributor-name {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin-top: var(--space-1);
}

.contributor-placeholder {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
}

.placeholder-avatar {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full);
  background: var(--bg-glass);
  animation: pulse 2s ease-in-out infinite;
}

.placeholder-text {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.view-all-contributors {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.view-all-contributors:hover {
  background: var(--bg-glass-hover);
  color: var(--color-primary);
  transform: translateY(-2px);
}

.view-all-contributors svg {
  width: 16px;
  height: 16px;
}

/* Roadmap Section */
.roadmap-section {
  margin-bottom: var(--space-16);
}

.roadmap-timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.roadmap-timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--gradient-primary);
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--space-8);
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-marker {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  background: var(--bg-glass);
  border: 3px solid var(--color-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 2;
}

.timeline-marker svg {
  width: 24px;
  height: 24px;
  color: var(--color-primary);
}

.timeline-content {
  flex: 1;
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
  max-width: calc(50% - 60px);
}

.timeline-item:nth-child(odd) .timeline-content {
  margin-right: auto;
}

.timeline-item:nth-child(even) .timeline-content {
  margin-left: auto;
}

.timeline-content:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.timeline-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.timeline-description {
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-3);
}

.timeline-status {
  display: inline-block;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timeline-status.completed {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.timeline-status.in-progress {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.timeline-status.planned {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

/* Philosophy Section */
.philosophy-section {
  margin-bottom: var(--space-16);
}

.philosophy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.philosophy-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
}

.philosophy-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.philosophy-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto var(--space-4);
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.philosophy-icon svg {
  width: 28px;
  height: 28px;
}

.philosophy-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.philosophy-description {
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Mobile Responsive for About Section */
@media (max-width: 768px) {
  .mission-principles {
    grid-template-columns: 1fr;
  }

  .team-grid {
    grid-template-columns: 1fr;
  }

  .roadmap-timeline::before {
    left: 30px;
  }

  .timeline-item {
    flex-direction: row !important;
    padding-left: 60px;
  }

  .timeline-item:nth-child(even) {
    flex-direction: row !important;
  }

  .timeline-marker {
    left: 30px;
    transform: translateX(-50%);
  }

  .timeline-content {
    max-width: none;
    margin: 0 !important;
  }

  .philosophy-grid {
    grid-template-columns: 1fr;
  }

  .contributors-list {
    grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
  }
}

/* ===== CONTACT SECTION ===== */
.contact-section {
  padding: var(--space-20) 0;
  background: var(--bg-primary);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
  align-items: start;
}

/* Contact Form */
.contact-form-container {
  position: sticky;
  top: var(--space-8);
}

.contact-form-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-10);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
}

.form-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  text-align: center;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.form-input,
.form-select,
.form-textarea {
  padding: var(--space-3) var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

.form-select {
  cursor: pointer;
}

.form-error {
  font-size: var(--text-xs);
  color: #ef4444;
  display: none;
}

.form-error.show {
  display: block;
}

/* Checkbox */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.form-checkbox {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-glass);
  border-radius: var(--radius-sm);
  background: var(--bg-glass);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.form-checkbox:checked + .checkbox-custom {
  background: var(--color-primary);
  border-color: var(--color-primary);
}

.form-checkbox:checked + .checkbox-custom::after {
  content: '✓';
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-text {
  flex: 1;
}

/* Submit Button */
.form-submit {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.form-submit:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.form-submit:active {
  transform: translateY(0);
}

.form-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submit-icon {
  width: 20px;
  height: 20px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.form-success {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: var(--radius-lg);
  color: #10b981;
  font-size: var(--text-sm);
  font-weight: 500;
}

.form-success svg {
  width: 16px;
  height: 16px;
}

/* Contact Info */
.contact-info-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.contact-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
}

.contact-info-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
  text-align: center;
}

.contact-info-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.contact-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto var(--space-4);
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.contact-icon svg {
  width: 28px;
  height: 28px;
}

.contact-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.contact-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  line-height: 1.5;
}

.contact-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.contact-link:hover {
  background: var(--bg-glass-hover);
  color: var(--color-primary);
  transform: translateY(-2px);
}

.contact-link svg {
  width: 16px;
  height: 16px;
}

/* Response Info */
.response-info {
  margin-top: var(--space-8);
}

.response-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
}

.response-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  text-align: center;
}

.response-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.response-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.response-type {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.response-time {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--color-primary);
}

/* Mobile Responsive for Contact Section */
@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }

  .contact-form-container {
    position: static;
  }

  .contact-info-grid {
    grid-template-columns: 1fr;
  }

  .response-grid {
    grid-template-columns: 1fr;
  }

  .response-item {
    flex-direction: column;
    gap: var(--space-1);
    text-align: center;
  }
}

/* ===== DOCUMENTATION SECTION ===== */
.docs-section {
  padding: var(--space-20) 0;
  background: var(--bg-secondary);
}

/* Documentation Search */
.docs-search {
  margin-bottom: var(--space-12);
  display: flex;
  justify-content: center;
}

.search-container {
  position: relative;
  max-width: 600px;
  width: 100%;
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
}

.search-input {
  width: 100%;
  padding: var(--space-4) var(--space-6) var(--space-4) var(--space-12);
  background: transparent;
  border: none;
  border-radius: var(--radius-2xl);
  color: var(--text-primary);
  font-size: var(--text-base);
  outline: none;
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.search-icon {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--text-secondary);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-xl);
  max-height: 400px;
  overflow-y: auto;
  z-index: 10;
  display: none;
}

.search-results.show {
  display: block;
}

.search-result-item {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--border-glass);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.search-result-item:hover {
  background: var(--bg-glass-hover);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-title {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.search-result-description {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.search-result-meta {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.search-result-category {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: 500;
}

.search-result-level {
  font-size: 10px;
  padding: 2px 6px;
}

/* Documentation Categories */
.docs-categories {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.docs-category {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.category-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.category-title svg {
  width: 32px;
  height: 32px;
  color: var(--color-primary);
}

.docs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-6);
}

.doc-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.doc-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.doc-icon {
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: var(--space-2);
}

.doc-icon svg {
  width: 28px;
  height: 28px;
}

.doc-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.doc-description {
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: 1.6;
  flex: 1;
}

.doc-meta {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin: var(--space-4) 0;
}

.doc-time {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.doc-level {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.doc-level.beginner {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.doc-level.intermediate {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.doc-level.advanced {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.doc-level.expert {
  background: rgba(139, 92, 246, 0.2);
  color: #8b5cf6;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.doc-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  align-self: flex-start;
}

.doc-link:hover {
  background: var(--bg-glass-hover);
  color: var(--color-primary);
  transform: translateY(-2px);
}

.doc-link svg {
  width: 16px;
  height: 16px;
}

/* Mobile Responsive for Documentation */
@media (max-width: 768px) {
  .docs-grid {
    grid-template-columns: 1fr;
  }

  .search-container {
    margin: 0 var(--space-4);
  }

  .category-title {
    font-size: var(--text-2xl);
  }

  .category-title svg {
    width: 24px;
    height: 24px;
  }
}

/* ===== FIREBASE UI COMPONENTS ===== */

/* Authentication Button */
.auth-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.auth-btn:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.auth-btn svg {
  width: 18px;
  height: 18px;
}

/* User Menu */
.user-menu-trigger {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.user-menu-btn {
  display: flex;
  align-items: center;
  padding: var(--space-1);
  background: transparent;
  border: none;
  border-radius: var(--radius-full);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.user-menu-btn:hover {
  background: var(--bg-glass);
  color: var(--text-primary);
}

.user-menu-btn svg {
  width: 16px;
  height: 16px;
}

.user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--space-2);
  min-width: 280px;
  z-index: 1000;
}

.user-menu-content {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-xl);
}

.user-profile-section {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-glass);
  margin-bottom: var(--space-4);
}

.user-avatar-container {
  position: relative;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  border: 2px solid var(--border-glass);
  object-fit: cover;
}

.user-name {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.user-email {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.user-menu-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.menu-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-2);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.menu-item:hover {
  background: var(--bg-glass-hover);
  color: var(--color-primary);
}

.menu-item.signout {
  color: #ef4444;
}

.menu-item.signout:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.menu-item svg {
  width: 18px;
  height: 18px;
}

.menu-divider {
  height: 1px;
  background: var(--border-glass);
  margin: var(--space-2) 0;
}

/* Authentication Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: var(--space-4);
}

.modal-content {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-xl);
  max-width: 480px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6) var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--border-glass);
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--bg-glass-hover);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-6);
}

/* Auth Tabs */
.auth-tabs {
  display: flex;
  gap: var(--space-1);
  margin-bottom: var(--space-6);
  background: var(--bg-glass);
  border-radius: var(--radius-lg);
  padding: var(--space-1);
}

.auth-tab {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  background: transparent;
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.auth-tab.active {
  background: var(--bg-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
}

.auth-tab:hover:not(.active) {
  color: var(--text-primary);
}

/* Auth Forms */
.auth-form {
  display: none;
}

.auth-form.active {
  display: block;
}

.social-auth {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.social-btn:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.google-btn:hover {
  border-color: #4285F4;
}

.github-btn:hover {
  border-color: #333;
}

.divider {
  display: flex;
  align-items: center;
  margin: var(--space-4) 0;
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--border-glass);
}

.divider span {
  padding: 0 var(--space-3);
}

.form-group {
  margin-bottom: var(--space-4);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input::placeholder {
  color: var(--text-secondary);
}

.auth-submit-btn {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.auth-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.auth-links {
  text-align: center;
  margin-top: var(--space-4);
}

.auth-links a {
  color: var(--color-primary);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: 500;
}

.auth-links a:hover {
  text-decoration: underline;
}

/* Auth Feedback */
.auth-feedback {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  color: white;
  font-size: var(--text-sm);
  font-weight: 500;
  z-index: 10001;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);
  display: none;
}

.auth-feedback.success {
  background: rgba(16, 185, 129, 0.9);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.auth-feedback.error {
  background: rgba(239, 68, 68, 0.9);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.auth-feedback.info {
  background: rgba(59, 130, 246, 0.9);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

/* Bookmark Button */
.bookmark-btn {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  width: 36px;
  height: 36px;
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-full);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.bookmark-btn:hover {
  background: var(--bg-glass-hover);
  color: var(--color-primary);
  transform: scale(1.1);
}

.bookmark-btn.bookmarked {
  background: var(--color-primary);
  color: white;
}

.bookmark-btn svg {
  width: 18px;
  height: 18px;
}

/* Rating Stars */
.rating-stars {
  display: flex;
  gap: var(--space-1);
  margin: var(--space-2) 0;
}

.star {
  font-size: 24px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: color var(--transition-fast);
}

.star:hover,
.star.active {
  color: #fbbf24;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  margin-top: var(--space-6);
}

.btn-secondary {
  padding: var(--space-2) var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-secondary:hover {
  background: var(--bg-glass-hover);
}

.btn-primary {
  padding: var(--space-2) var(--space-4);
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  font-size: var(--text-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Mobile Responsive for Firebase UI */
@media (max-width: 768px) {
  .modal-content {
    margin: var(--space-2);
    max-width: none;
  }

  .modal-header,
  .modal-body {
    padding: var(--space-4);
  }

  .user-menu {
    right: var(--space-2);
    left: var(--space-2);
    min-width: auto;
  }

  .auth-btn span {
    display: none;
  }

  .social-btn {
    font-size: var(--text-xs);
    padding: var(--space-2) var(--space-3);
  }
}

/* ===== FEATURES SECTION ===== */
.features-section {
  padding: var(--space-20) 0;
  background: var(--bg-primary);
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-title {
  font-size: var(--text-4xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.section-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
}

.feature-tags {
  display: flex;
  gap: var(--space-2);
  margin-top: var(--space-4);
  flex-wrap: wrap;
}

.tag {
  padding: var(--space-1) var(--space-3);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
  color: var(--text-secondary);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* ===== INSTALLATION SECTION ===== */
.installation-section {
  padding: var(--space-20) 0;
  background: var(--bg-secondary);
}

.installation-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  max-width: 100%;
}

.step-card {
  display: flex;
  gap: var(--space-6);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
  min-width: 0; /* Allow flex items to shrink below content size */
  max-width: 100%;
}

.step-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.step-number {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  color: white;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  font-weight: 700;
}

.step-content {
  flex: 1;
  min-width: 0; /* Allow flex item to shrink below content size */
  max-width: 100%;
}

.step-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

/* ===== DOCS SECTION ===== */
.docs-section {
  padding: var(--space-20) 0;
  background: var(--bg-primary);
}

.docs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.doc-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
  text-align: center;
}

.doc-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-4);
  background: var(--gradient-primary);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.doc-icon svg {
  width: 32px;
  height: 32px;
}

.doc-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.doc-description {
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  line-height: 1.6;
}

.doc-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.doc-link:hover {
  color: var(--color-secondary);
  transform: translateX(4px);
}

.doc-link svg {
  width: 16px;
  height: 16px;
}

/* ===== EXAMPLES SECTION ===== */
.examples-section {
  padding: var(--space-20) 0;
  background: var(--bg-secondary);
}

.examples-container {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
}

.example-tabs {
  display: flex;
  background: var(--bg-glass-hover);
  border-bottom: 1px solid var(--border-glass);
  overflow-x: auto;
}

.example-content {
  padding: var(--space-8);
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
}

.example-demo {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.demo-controls {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.demo-status {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  padding: var(--space-2) var(--space-3);
  background: var(--bg-glass);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-glass);
}

.demo-output {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  min-height: 200px;
  overflow-y: auto;
}

.output-placeholder {
  color: var(--text-tertiary);
  font-style: italic;
  text-align: center;
  padding: var(--space-8);
}

.example-code {
  /* Styles already defined in components.css */
}

/* ===== COMMUNITY SECTION ===== */
.community-section {
  padding: var(--space-20) 0;
  background: var(--bg-primary);
}

.community-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-16);
}

.community-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
}

.community-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-4);
  background: var(--gradient-primary);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.community-icon svg {
  width: 32px;
  height: 32px;
}

.community-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.community-description {
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  line-height: 1.6;
}

.community-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.community-link:hover {
  color: var(--color-secondary);
  transform: translateY(-2px);
}

.community-link svg {
  width: 16px;
  height: 16px;
}

.contributors-section {
  text-align: center;
}

.contributors-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.contributors-subtitle {
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
}

.contributors-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--space-4);
}

.contributor-placeholder {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.contributor-avatar-placeholder {
  width: 40px;
  height: 40px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.contributor-info-placeholder {
  flex: 1;
}

.contributor-name-placeholder {
  height: 16px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-2);
}

.contributor-contributions-placeholder {
  height: 12px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  width: 60%;
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 var(--space-4);
  }

  .hero-content {
    padding: 0 var(--space-4);
  }

  .container {
    padding: 0 var(--space-4);
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .installation-steps {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .step-card {
    flex-direction: column;
    text-align: center;
    padding: var(--space-6);
    gap: var(--space-4);
  }

  .step-content {
    max-width: 100%;
  }

  .code-snippet {
    margin: var(--space-3) 0;
  }

  .code-snippet-content {
    padding: var(--space-3);
    font-size: calc(var(--text-sm) * 0.9);
  }

  .tab-panel.active {
    grid-template-columns: 1fr;
  }

  .docs-grid {
    grid-template-columns: 1fr;
  }

  .community-grid {
    grid-template-columns: 1fr;
  }
}

/* Additional responsive styles for very small screens */
@media (max-width: 320px) {
  .installation-steps {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .step-card {
    padding: var(--space-4);
    gap: var(--space-3);
  }

  .code-snippet-content {
    padding: var(--space-2);
    font-size: calc(var(--text-sm) * 0.85);
  }

  .installation-preview {
    max-width: 100%;
  }
}

/* ===== FOOTER ===== */
.footer {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-glass);
  padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.footer-section {
  /* Base styles for footer sections */
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.footer-logo {
  width: 32px;
  height: 32px;
  color: var(--color-primary);
}

.footer-brand-text {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.footer-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.footer-social {
  display: flex;
  gap: var(--space-3);
}

.social-link {
  width: 40px;
  height: 40px;
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.social-link:hover {
  color: var(--color-primary);
  background: var(--bg-glass-hover);
  transform: translateY(-2px);
}

.social-link svg {
  width: 20px;
  height: 20px;
}

.footer-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: var(--space-2);
}

.footer-link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-link:hover {
  color: var(--color-primary);
}

.footer-bottom {
  border-top: 1px solid var(--border-glass);
  padding-top: var(--space-6);
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.footer-copyright {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.footer-copyright a {
  color: var(--color-primary);
  text-decoration: none;
}

.footer-copyright a:hover {
  text-decoration: underline;
}

.footer-badges {
  display: flex;
  gap: var(--space-2);
}

/* ===== KEY EXCHANGE VISUALIZER ===== */
.key-exchange-visualizer {
  padding: var(--space-20) 0;
  background: var(--bg-secondary);
}

.exchange-stage {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: var(--space-8);
  align-items: center;
  margin-bottom: var(--space-12);
}

.participant {
  text-align: center;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
}

.avatar {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-2xl);
  font-weight: 700;
  color: white;
  transition: all var(--transition-normal);
}

.alice-avatar {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.bob-avatar {
  background: linear-gradient(135deg, #8b5cf6, #6d28d9);
}

.key-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--color-success);
  border: 2px solid white;
  opacity: 0;
  transition: all var(--transition-normal);
}

.participant-name {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.key-display {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  min-width: 200px;
}

.key-item {
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  margin-bottom: var(--space-2);
  color: var(--text-secondary);
}

.key-value {
  color: var(--color-primary);
  font-weight: 500;
}

.exchange-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-6);
}

.data-flow {
  width: 100%;
  height: 200px;
}

.exchange-controls {
  text-align: center;
}

.exchange-status {
  margin-top: var(--space-4);
  font-size: var(--text-sm);
  color: var(--text-secondary);
  padding: var(--space-2) var(--space-4);
  background: var(--bg-glass);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-glass);
}

.exchange-explanation {
  text-align: center;
}

.step-indicator {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.step {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--text-tertiary);
  transition: all var(--transition-normal);
}

.step.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.step.completed {
  background: var(--color-success);
  border-color: var(--color-success);
  color: white;
}

.step-description {
  font-size: var(--text-lg);
  color: var(--text-primary);
  font-weight: 500;
}

/* ===== CURVE SELECTOR ===== */
.curve-selector-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-8);
  padding: var(--space-6);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.curve-wheel {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto;
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.curve-option {
  position: absolute;
  width: 80px;
  height: 80px;
  background: var(--bg-glass);
  border: 2px solid var(--border-glass);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.curve-option:nth-child(1) { top: 0; left: 50%; transform: translateX(-50%); }
.curve-option:nth-child(2) { top: 50%; right: 0; transform: translateY(-50%); }
.curve-option:nth-child(3) { bottom: 0; left: 50%; transform: translateX(-50%); }
.curve-option:nth-child(4) { top: 50%; left: 0; transform: translateY(-50%); }

.curve-option:hover {
  background: var(--bg-glass-hover);
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.curve-icon {
  font-size: var(--text-xl);
  margin-bottom: var(--space-1);
}

.curve-name {
  font-size: var(--text-xs);
  font-weight: 500;
  color: var(--text-primary);
}

.curve-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.curve-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.curve-parameters {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.parameter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
}

.param-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.param-value {
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: var(--color-primary);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ===== WASM VISUALIZER ===== */
.wasm-visualizer {
  margin-top: var(--space-6);
  padding: var(--space-4);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.pipeline-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.pipeline-stage {
  text-align: center;
  padding: var(--space-3);
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.pipeline-stage.active {
  background: var(--color-primary);
  color: white;
  transform: scale(1.05);
}

.stage-icon {
  font-size: var(--text-xl);
  margin-bottom: var(--space-2);
}

.stage-name {
  font-size: var(--text-sm);
  font-weight: 500;
  margin-bottom: var(--space-2);
}

.stage-progress {
  height: 4px;
  background: var(--bg-primary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.stage-progress::after {
  content: '';
  display: block;
  height: 100%;
  width: 0%;
  background: var(--color-success);
  transition: width 0.5s ease-out;
}

.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) var(--space-3);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
}

.metric-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.metric-value {
  font-family: var(--font-family-mono);
  font-weight: 600;
  color: var(--color-primary);
}

/* ===== ANIMATIONS ===== */
@keyframes drawPath {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px var(--color-success);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px var(--color-success);
    transform: scale(1.1);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .exchange-stage {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .curve-selector-container {
    grid-template-columns: 1fr;
  }

  .pipeline-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }
}

/* ===== ACCESSIBILITY ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* ===== BROWSER COMPATIBILITY FIXES ===== */

/* Fix for Safari backdrop-filter */
@supports (-webkit-backdrop-filter: blur(20px)) {
  .stat-card,
  .philosophy-card,
  .search-container,
  .auth-btn,
  .doc-card,
  .examples-container,
  .example-demo {
    -webkit-backdrop-filter: blur(20px);
  }
}

/* Fix for older browsers without CSS Grid support */
@supports not (display: grid) {
  .stats-grid,
  .features-grid,
  .philosophy-grid,
  .docs-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-6);
  }

  .stat-card,
  .feature-card,
  .philosophy-card,
  .doc-card {
    flex: 1 1 300px;
    min-width: 300px;
  }
}

/* Fix for browsers without CSS custom properties */
@supports not (color: var(--color-primary)) {
  :root {
    color: #3b82f6; /* fallback for --color-primary */
  }

  .hero-title {
    color: #0f172a; /* fallback for --text-primary */
  }

  .cta-button.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  }
}

/* Fix for browsers without clip-path support */
@supports not (clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)) {
  .morph-button {
    border-radius: var(--radius-lg);
  }

  .morph-button:hover {
    transform: scale(1.05);
  }
}

/* ===== RESPONSIVE FOOTER ===== */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-social {
    justify-content: center;
  }
}
