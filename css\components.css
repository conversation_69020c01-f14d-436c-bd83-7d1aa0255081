/* ===== COMPONENT STYLES ===== */

/* ===== CARDS ===== */
.card {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  background: var(--bg-glass-hover);
}

.card-header {
  margin-bottom: var(--space-4);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.card-content {
  margin-bottom: var(--space-4);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-glass);
}

/* ===== FEATURE CARDS ===== */
.feature-card {
  position: relative;
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-12px);
  box-shadow: var(--shadow-xl);
  background: var(--bg-glass-hover);
}

.feature-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-4);
  background: var(--gradient-primary);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-2xl);
  color: white;
  transition: all var(--transition-normal);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.feature-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--bg-glass);
  color: var(--text-primary);
  border: 1px solid var(--border-glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
}

.btn-outline:hover {
  background: var(--color-primary);
  color: white;
}

.btn-ghost {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid transparent;
}

.btn-ghost:hover {
  background: var(--bg-glass);
  border-color: var(--border-glass);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
}

.btn-icon {
  width: 20px;
  height: 20px;
  transition: transform var(--transition-fast);
}

.btn:hover .btn-icon {
  transform: translateX(2px);
}

/* ===== BADGES ===== */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.badge-primary {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.badge-success {
  background: var(--color-success);
  color: white;
  border-color: var(--color-success);
}

.badge-warning {
  background: var(--color-warning);
  color: white;
  border-color: var(--color-warning);
}

.badge-error {
  background: var(--color-error);
  color: white;
  border-color: var(--color-error);
}

/* ===== CODE BLOCKS ===== */
.code-snippet {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-xl);
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-glass);
  margin: var(--space-4) 0;
  max-width: 100%;
  min-width: 0; /* Allow shrinking below content size */
}

.code-snippet-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-glass-hover);
  border-bottom: 1px solid var(--border-glass);
}

.code-snippet-title {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.code-snippet-actions {
  display: flex;
  gap: var(--space-2);
}

.code-snippet-content {
  padding: var(--space-4);
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  line-height: 1.5;
  color: var(--text-primary);
  overflow-x: auto;
  overflow-y: hidden;
  max-width: 100%;
  min-width: 0;
  /* Ensure scrollbar is visible when needed */
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary) var(--bg-tertiary);
}

.code-snippet-content pre {
  margin: 0;
  background: transparent;
  border: none;
  padding: 0;
  white-space: pre;
  overflow-x: auto;
  overflow-y: hidden;
  max-width: 100%;
  min-width: 0;
}

.code-snippet-content code {
  font-family: inherit;
  font-size: inherit;
  background: transparent;
  padding: 0;
  border: none;
  white-space: pre;
  word-wrap: normal;
  overflow-wrap: normal;
}

/* Custom scrollbar styles for code blocks */
.code-snippet-content::-webkit-scrollbar {
  height: 8px;
}

.code-snippet-content::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

.code-snippet-content::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 4px;
  opacity: 0.7;
}

.code-snippet-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-secondary);
  opacity: 1;
}

.code-snippet-content pre::-webkit-scrollbar {
  height: 8px;
}

.code-snippet-content pre::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

.code-snippet-content pre::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 4px;
  opacity: 0.7;
}

.code-snippet-content pre::-webkit-scrollbar-thumb:hover {
  background: var(--color-secondary);
  opacity: 1;
}

/* ===== TOOLTIPS ===== */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
  z-index: var(--z-tooltip);
  margin-bottom: var(--space-2);
  box-shadow: var(--shadow-lg);
}

.tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: var(--bg-tertiary);
}

.tooltip:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
}

/* ===== PROGRESS BARS ===== */
.progress {
  width: 100%;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ===== MODALS ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal {
  background: var(--bg-primary);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  transform: scale(0.9);
  transition: transform var(--transition-normal);
}

.modal-overlay.active .modal {
  transform: scale(1);
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-glass);
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.modal-content {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-glass);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
}

/* ===== TABS ===== */
.tabs {
  border-bottom: 1px solid var(--border-glass);
}

.tab-list {
  display: flex;
  gap: var(--space-1);
}

.tab-button {
  padding: var(--space-3) var(--space-4);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  transition: all var(--transition-fast);
  position: relative;
}

.tab-button:hover {
  color: var(--text-primary);
  background: var(--bg-glass);
}

.tab-button.active {
  color: var(--color-primary);
  background: var(--bg-glass);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-primary);
}

.tab-content {
  padding: var(--space-6) 0;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
  animation: fadeInUp 0.3s ease-out;
}

/* ===== ACCORDIONS ===== */
.accordion-item {
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-2);
  overflow: hidden;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.accordion-header {
  padding: var(--space-4);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color var(--transition-fast);
}

.accordion-header:hover {
  background: var(--bg-glass-hover);
}

.accordion-title {
  font-weight: 500;
  color: var(--text-primary);
}

.accordion-icon {
  width: 20px;
  height: 20px;
  color: var(--text-secondary);
  transition: transform var(--transition-fast);
}

.accordion-item.active .accordion-icon {
  transform: rotate(180deg);
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal);
}

.accordion-item.active .accordion-content {
  max-height: 500px;
}

.accordion-body {
  padding: 0 var(--space-4) var(--space-4);
  color: var(--text-secondary);
  line-height: 1.6;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .card {
    padding: var(--space-4);
  }

  .feature-card {
    padding: var(--space-6);
  }

  .btn {
    padding: var(--space-3) var(--space-5);
  }

  .modal {
    margin: var(--space-4);
  }

  .tab-list {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .tab-list::-webkit-scrollbar {
    display: none;
  }

  .code-snippet-content {
    padding: var(--space-3);
    font-size: calc(var(--text-sm) * 0.9);
  }

  .code-snippet-header {
    padding: var(--space-2) var(--space-3);
  }

  .code-snippet-title {
    font-size: calc(var(--text-sm) * 0.9);
  }
}

/* Additional responsive styles for code blocks on very small screens */
@media (max-width: 320px) {
  .code-snippet-content {
    padding: var(--space-2);
    font-size: calc(var(--text-sm) * 0.85);
  }

  .code-snippet-header {
    padding: var(--space-2);
    flex-wrap: wrap;
    gap: var(--space-2);
  }

  .code-snippet-title {
    font-size: calc(var(--text-sm) * 0.85);
  }
}
