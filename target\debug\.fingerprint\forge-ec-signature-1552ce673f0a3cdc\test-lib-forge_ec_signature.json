{"rustc": 16591470773350601817, "features": "[\"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\", \"test-utils\"]", "target": 12987313468403878510, "profile": 1235621381506040755, "path": 9125880921305846468, "deps": [[530211389790465181, "hex", false, 7681383813274821952], [5994642257694095480, "forge_ec_core", false, 4672930868244505188], [6528079939221783635, "zeroize", false, 14007274721335332805], [9209347893430674936, "hmac", false, 154507136758044380], [9857275760291862238, "sha2", false, 10584952717619411474], [13326029724636836038, "forge_ec_rng", false, 1593188742936531794], [14350836257056350918, "forge_ec_curves", false, 16728854025161165635], [15063701716282871955, "forge_ec_hash", false, 14531012125686755842], [17003143334332120809, "subtle", false, 4917266028617495969], [17475753849556516473, "digest", false, 16341302406531691146], [18130209639506977569, "rand_core", false, 2072236099581160293]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\forge-ec-signature-1552ce673f0a3cdc\\dep-test-lib-forge_ec_signature", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}