{"rustc": 16591470773350601817, "features": "[\"default\", \"hex\", \"hex-literal\", \"std\", \"test-utils\"]", "declared_features": "[\"alloc\", \"default\", \"hex\", \"hex-literal\", \"std\", \"test-utils\"]", "target": 14952468957906770697, "profile": 13136499164997004105, "path": 14309710185945734409, "deps": [[530211389790465181, "hex", false, 17088062159765854285], [5994642257694095480, "forge_ec_core", false, 11215900732348816337], [6528079939221783635, "zeroize", false, 2745998783931530775], [8632578124021956924, "hex_literal", false, 1312211865478306089], [9209347893430674936, "hmac", false, 15271584293396208319], [9857275760291862238, "sha2", false, 15044962170313179095], [9920160576179037441, "getrandom", false, 16754706667903359090], [14350836257056350918, "forge_ec_curves", false, 10294871808448728720], [15063701716282871955, "forge_ec_hash", false, 5416292730735005866], [17003143334332120809, "subtle", false, 2669115221904121209], [18130209639506977569, "rand_core", false, 2614454316829272266]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\forge-ec-rng-1d4ab9ad668320d9\\dep-lib-forge_ec_rng", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}